import { buildApiUrl, getBaseUrl, flowmakerUrls, environment, getFlowIdFromUrl } from './apiUrl';

// Mock window object for testing
const mockWindow = (url: string, baseUrl?: string) => {
  Object.defineProperty(window, 'location', {
    value: new URL(url),
    writable: true,
  });
  
  if (baseUrl) {
    (window as any).baseUrl = baseUrl;
  }
};

describe('API URL Utilities', () => {
  beforeEach(() => {
    // Reset window.baseUrl
    delete (window as any).baseUrl;
  });

  describe('getBaseUrl', () => {
    it('should return window.baseUrl when available', () => {
      (window as any).baseUrl = 'https://demo.zaptra.in';
      expect(getBaseUrl()).toBe('https://demo.zaptra.in');
    });

    it('should fallback to window.location.origin', () => {
      mockWindow('https://localhost:3000/flowmaker/edit/123');
      expect(getBaseUrl()).toBe('https://localhost:3000');
    });
  });

  describe('buildApiUrl', () => {
    it('should build correct URL for production', () => {
      (window as any).baseUrl = 'https://demo.zaptra.in';
      const result = buildApiUrl('/flowmaker/update/123');
      expect(result).toBe('https://demo.zaptra.in/flowmaker/update/123');
    });

    it('should build correct URL for localhost', () => {
      (window as any).baseUrl = 'http://localhost:8000';
      const result = buildApiUrl('/flowmaker/update/123');
      expect(result).toBe('http://localhost:8000/flowmaker/update/123');
    });

    it('should handle paths without leading slash', () => {
      (window as any).baseUrl = 'https://demo.zaptra.in';
      const result = buildApiUrl('flowmaker/update/123');
      expect(result).toBe('https://demo.zaptra.in/flowmaker/update/123');
    });

    it('should handle base URL with trailing slash', () => {
      (window as any).baseUrl = 'https://demo.zaptra.in/';
      const result = buildApiUrl('/flowmaker/update/123');
      expect(result).toBe('https://demo.zaptra.in/flowmaker/update/123');
    });
  });

  describe('flowmakerUrls', () => {
    it('should generate correct update flow URL', () => {
      expect(flowmakerUrls.updateFlow(123)).toBe('/flowmaker/update/123');
      expect(flowmakerUrls.updateFlow('abc')).toBe('/flowmaker/update/abc');
    });

    it('should generate correct training data URL', () => {
      expect(flowmakerUrls.trainingData(123)).toBe('/ai/training-data/123');
    });

    it('should generate correct process URLs', () => {
      expect(flowmakerUrls.processWebsite()).toBe('/ai/process-website');
      expect(flowmakerUrls.processFaq()).toBe('/ai/process-faq');
      expect(flowmakerUrls.processFile()).toBe('/ai/process-file');
    });

    it('should generate correct delete document URL', () => {
      expect(flowmakerUrls.deleteDocument(456)).toBe('/ai/document/456');
    });

    it('should generate correct upload media URL', () => {
      expect(flowmakerUrls.uploadMedia()).toBe('/flowmakermedia');
    });
  });

  describe('environment detection', () => {
    it('should detect localhost correctly', () => {
      mockWindow('http://localhost:8000/flowmaker/edit/123');
      expect(environment.isLocalhost()).toBe(true);
      expect(environment.isProduction()).toBe(false);
      expect(environment.getEnvironmentName()).toBe('localhost');
    });

    it('should detect production correctly', () => {
      mockWindow('https://demo.zaptra.in/flowmaker/edit/123');
      expect(environment.isLocalhost()).toBe(false);
      expect(environment.isProduction()).toBe(true);
      expect(environment.getEnvironmentName()).toBe('production');
    });

    it('should detect 127.0.0.1 as localhost', () => {
      mockWindow('http://127.0.0.1:8000/flowmaker/edit/123');
      expect(environment.isLocalhost()).toBe(true);
    });

    it('should detect local IP as localhost', () => {
      mockWindow('http://*************:8000/flowmaker/edit/123');
      expect(environment.isLocalhost()).toBe(true);
    });
  });

  describe('getFlowIdFromUrl', () => {
    it('should extract flow ID from standard flowmaker URL', () => {
      mockWindow('http://localhost:8000/flowmaker/edit/123');
      expect(getFlowIdFromUrl()).toBe('123');
    });

    it('should extract flow ID from production URL with subdirectory', () => {
      mockWindow('https://demo.zaptra.in/zaptra/flowmaker/edit/456');
      expect(getFlowIdFromUrl()).toBe('456');
    });

    it('should fallback to last segment if flowmaker not found', () => {
      mockWindow('https://example.com/some/other/path/789');
      expect(getFlowIdFromUrl()).toBe('789');
    });

    it('should return null for empty path', () => {
      mockWindow('https://example.com/');
      expect(getFlowIdFromUrl()).toBe('');
    });
  });
});

describe('Integration Tests', () => {
  it('should work end-to-end for localhost', () => {
    mockWindow('http://localhost:8000/flowmaker/edit/123');
    (window as any).baseUrl = 'http://localhost:8000';
    
    const flowId = getFlowIdFromUrl();
    const updateUrl = flowmakerUrls.updateFlow(flowId!);
    const fullUrl = buildApiUrl(updateUrl);
    
    expect(fullUrl).toBe('http://localhost:8000/flowmaker/update/123');
  });

  it('should work end-to-end for production', () => {
    mockWindow('https://demo.zaptra.in/zaptra/flowmaker/edit/456');
    (window as any).baseUrl = 'https://demo.zaptra.in';
    
    const flowId = getFlowIdFromUrl();
    const updateUrl = flowmakerUrls.updateFlow(flowId!);
    const fullUrl = buildApiUrl(updateUrl);
    
    expect(fullUrl).toBe('https://demo.zaptra.in/flowmaker/update/456');
  });
});
