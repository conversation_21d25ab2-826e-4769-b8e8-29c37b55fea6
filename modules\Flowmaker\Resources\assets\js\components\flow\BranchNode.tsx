import React, { useState, use<PERSON><PERSON>back, useEffect } from 'react';
import { <PERSON><PERSON>, Position, useReactFlow } from '@xyflow/react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { GitFork, Plus, X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { BranchCondition, WebhookVariable, NodeData } from '@/types/flow';
import { useFlowVariables } from '@/hooks/useFlowVariables';

interface BranchNodeProps {
  id: string;
  data: NodeData;
  selected?: boolean;
}



const operators = [
  { value: 'equals', label: 'Equals' },
  { value: 'not_equals', label: 'Not Equals' },
  { value: 'greater_than', label: 'Greater Than' },
  { value: 'less_than', label: 'Less Than' },
  { value: 'contains', label: 'Contains' },
  { value: 'not_contains', label: 'Not Contains' },
] as const;

// Deep clone utility function to prevent shared references
const deepCloneConditions = (conditions: BranchCondition[], nodeId: string): BranchCondition[] => {
  return conditions.map(condition => {
    // Check if this condition ID belongs to a different node
    // A condition ID should start with the current node's ID
    const belongsToThisNode = condition.id.startsWith(`${nodeId}_cond_`);

    return {
      id: belongsToThisNode ? condition.id : generateConditionId(nodeId),
      variableId: condition.variableId,
      operator: condition.operator,
      value: condition.value,
    };
  });
};

// Generate unique condition ID for this specific branch node
const generateConditionId = (nodeId: string): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `${nodeId}_cond_${timestamp}_${random}`;
};

const BranchNode = ({ id, data }: BranchNodeProps) => {
  const { setNodes } = useReactFlow();
  const { groupedVariables } = useFlowVariables();

  // Initialize with deep cloned conditions to prevent shared references
  const [conditions, setConditions] = useState<BranchCondition[]>(() => {
    const initialConditions = data.settings?.conditions || [];
    return deepCloneConditions(initialConditions, id);
  });

  // Sync local state with prop changes using useEffect
  useEffect(() => {
    const newConditions = data.settings?.conditions || [];
    // Always ensure conditions have proper IDs for this node
    const properConditions = deepCloneConditions(newConditions, id);

    // Only update if the conditions have actually changed
    if (JSON.stringify(properConditions) !== JSON.stringify(conditions)) {
      setConditions(properConditions);
    }
  }, [data.settings?.conditions, id]);

  const updateNodeData = useCallback((newConditions: BranchCondition[]) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        // Use the id prop instead of data.id for more reliable identification
        if (node.id === id) {
          return {
            ...node,
            data: {
              ...node.data,
              settings: {
                ...node.data.settings,
                conditions: newConditions,
              },
            },
          };
        }
        return node;
      })
    );
  }, [id, setNodes]);

  const addCondition = () => {
    const newCondition: BranchCondition = {
      id: generateConditionId(id), // Use node-specific ID generation
      variableId: '',
      operator: 'equals',
      value: '',
    };
    // Create a deep clone of existing conditions and add the new one
    const newConditions = deepCloneConditions([...conditions, newCondition], id);
    setConditions(newConditions);
    updateNodeData(newConditions);
  };

  const removeCondition = (conditionId: string) => {
    // Create a deep clone of filtered conditions
    const filteredConditions = conditions.filter((c) => c.id !== conditionId);
    const newConditions = deepCloneConditions(filteredConditions, id);
    setConditions(newConditions);
    updateNodeData(newConditions);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      <Handle 
        type="target" 
        position={Position.Left}
        className="!bg-gray-300 !w-3 !h-3 !rounded-full"
      />
      
      <div className="flex items-center gap-2 mb-4 pb-2 border-b border-gray-100 px-4 pt-3 bg-gray-50">
        <GitFork className="h-4 w-4 text-purple-600" />
        <div className="font-medium">Branch</div>
      </div>

      <div className="p-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Branch</h3>
            <Button variant="outline" size="sm" onClick={addCondition}>
              <Plus className="h-4 w-4 mr-1" />
              Add Condition
            </Button>
          </div>

          <div className="space-y-3">
            {conditions.map((condition) => (
              <div key={condition.id} className="flex items-center gap-2">
                <Select
                  value={condition.variableId}
                  onValueChange={(value) => {
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, variableId: value } : c
                    );
                    const clonedConditions = deepCloneConditions(updatedConditions, id);
                    setConditions(clonedConditions);
                    updateNodeData(clonedConditions);
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Variable" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(groupedVariables).map(([category, categoryVariables]) => (
                      <div key={category}>
                        <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                          {category}
                        </div>
                        {categoryVariables.map((variable) => (
                          <SelectItem key={variable.value} value={variable.value}>
                            {variable.label}
                          </SelectItem>
                        ))}
                      </div>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={condition.operator}
                  onValueChange={(value: BranchCondition['operator']) => {
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, operator: value } : c
                    );
                    const clonedConditions = deepCloneConditions(updatedConditions, id);
                    setConditions(clonedConditions);
                    updateNodeData(clonedConditions);
                  }}
                >
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Operator" />
                  </SelectTrigger>
                  <SelectContent>
                    {operators.map((op) => (
                      <SelectItem key={op.value} value={op.value}>
                        {op.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Input
                  placeholder="Value"
                  value={condition.value}
                  onChange={(e) => {
                    const updatedConditions = conditions.map((c) =>
                      c.id === condition.id ? { ...c, value: e.target.value } : c
                    );
                    const clonedConditions = deepCloneConditions(updatedConditions, id);
                    setConditions(clonedConditions);
                    updateNodeData(clonedConditions);
                  }}
                  className="flex-1"
                />

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeCondition(condition.id)}
                >
                  <X className="h-4 w-4" />
                </Button>

                <div className="flex flex-col gap-4 ml-2">
                  <Handle
                    type="source"
                    position={Position.Right}
                    id={`condition-${condition.id}-true`}
                    className="!bg-green-500 !w-3 !h-3 !rounded-full"
                    style={{ top: '25%' }}
                  />
                  <Handle
                    type="source"
                    position={Position.Right}
                    id={`condition-${condition.id}-false`}
                    className="!bg-red-500 !w-3 !h-3 !rounded-full"
                    style={{ top: '75%' }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BranchNode;