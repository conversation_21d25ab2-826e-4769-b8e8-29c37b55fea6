/**
 * Test script to verify the flow data migration works correctly
 */

import { fixSharedConditionIds, hasSharedConditionIds, validateFlowData } from './flowDataMigration';

// Test data that simulates the issue from the console logs
const testFlowDataWithSharedIds = {
  nodes: [
    {
      id: "branch-0.7439814461370684",
      type: "branch",
      position: { x: 144.7914502100703, y: -1091.1842149316833 },
      data: {
        label: "Branch",
        type: "branch",
        settings: {
          webhookVariables: [],
          conditions: [
            {
              id: "branch-0.8212877124900572_cond_687b6ec3550cf5.31861035", // SHARED ID
              variableId: "contact_last_message",
              operator: "equals",
              value: "vinee"
            }
          ]
        }
      },
      measured: { width: 630, height: 183 },
      selected: false,
      dragging: false
    },
    {
      id: "branch-0.8212877124900572",
      type: "branch", 
      position: { x: 129.29686724094776, y: -690.2729755874326 },
      data: {
        label: "Branch",
        type: "branch",
        settings: {
          webhookVariables: [],
          conditions: [
            {
              id: "branch-0.8212877124900572_cond_687b6ec3550cf5.31861035", // SAME SHARED ID
              variableId: "contact_last_message",
              operator: "equals", 
              value: "vinee"
            }
          ]
        }
      },
      measured: { width: 630, height: 183 },
      selected: true,
      dragging: false
    },
    {
      id: "branch-0.7868949944308767",
      type: "branch",
      position: { x: 139.81720321987052, y: -888.6015627947972 },
      data: {
        label: "Branch",
        type: "branch", 
        settings: {
          webhookVariables: [],
          conditions: [
            {
              id: "branch-0.8212877124900572_cond_687b6ec3550cf5.31861035", // SAME SHARED ID AGAIN
              variableId: "contact_last_message",
              operator: "equals",
              value: "vinee"
            }
          ]
        }
      },
      measured: { width: 630, height: 183 },
      selected: false,
      dragging: false
    }
  ],
  edges: []
};

// Test the migration
export const runMigrationTest = () => {
  console.log('🧪 Running Flow Data Migration Test...');
  console.log('=====================================');
  
  // Test 1: Detect shared condition IDs
  console.log('Test 1: Detecting shared condition IDs');
  const hasShared = hasSharedConditionIds(testFlowDataWithSharedIds);
  console.log(`Has shared condition IDs: ${hasShared}`);
  console.assert(hasShared === true, 'Should detect shared condition IDs');
  
  // Test 2: Validate problematic data
  console.log('\nTest 2: Validating problematic data');
  const validation = validateFlowData(testFlowDataWithSharedIds);
  console.log('Validation result:', validation);
  console.assert(validation.isValid === false, 'Should detect validation issues');
  console.assert(validation.stats.sharedConditionIds.length > 0, 'Should find shared condition IDs');
  
  // Test 3: Fix the shared condition IDs
  console.log('\nTest 3: Fixing shared condition IDs');
  const fixedData = fixSharedConditionIds(testFlowDataWithSharedIds);
  
  // Test 4: Verify the fix worked
  console.log('\nTest 4: Verifying the fix');
  const hasSharedAfterFix = hasSharedConditionIds(fixedData);
  console.log(`Has shared condition IDs after fix: ${hasSharedAfterFix}`);
  console.assert(hasSharedAfterFix === false, 'Should not have shared condition IDs after fix');
  
  // Test 5: Validate fixed data
  console.log('\nTest 5: Validating fixed data');
  const validationAfterFix = validateFlowData(fixedData);
  console.log('Validation after fix:', validationAfterFix);
  console.assert(validationAfterFix.isValid === true, 'Should pass validation after fix');
  
  // Test 6: Verify each node has unique condition IDs
  console.log('\nTest 6: Verifying unique condition IDs per node');
  const conditionIds = new Set();
  let allUnique = true;
  
  for (const node of fixedData.nodes) {
    if (node.type === 'branch' && node.data.settings?.conditions) {
      for (const condition of node.data.settings.conditions) {
        if (conditionIds.has(condition.id)) {
          allUnique = false;
          console.error(`Duplicate condition ID found: ${condition.id}`);
        } else {
          conditionIds.add(condition.id);
          console.log(`✅ Unique condition ID: ${condition.id} (Node: ${node.id})`);
        }
        
        // Verify the condition ID belongs to the correct node
        if (!condition.id.startsWith(node.id)) {
          console.warn(`⚠️ Condition ID doesn't match node: ${condition.id} (Node: ${node.id})`);
        }
      }
    }
  }
  
  console.assert(allUnique === true, 'All condition IDs should be unique');
  
  // Test 7: Compare before and after
  console.log('\nTest 7: Before vs After comparison');
  console.log('Before migration:');
  testFlowDataWithSharedIds.nodes.forEach(node => {
    if (node.type === 'branch' && node.data.settings?.conditions) {
      node.data.settings.conditions.forEach(condition => {
        console.log(`  Node ${node.id}: Condition ${condition.id}`);
      });
    }
  });
  
  console.log('\nAfter migration:');
  fixedData.nodes.forEach(node => {
    if (node.type === 'branch' && node.data.settings?.conditions) {
      node.data.settings.conditions.forEach(condition => {
        console.log(`  Node ${node.id}: Condition ${condition.id}`);
      });
    }
  });
  
  console.log('\n✅ All tests passed! Migration is working correctly.');
  
  return {
    originalData: testFlowDataWithSharedIds,
    fixedData: fixedData,
    validationBefore: validation,
    validationAfter: validationAfterFix
  };
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).runMigrationTest = runMigrationTest;
  console.log('🔧 Migration test available as window.runMigrationTest()');
}
