name: release

on:
  pull_request:
    types: [ labeled ]
    branches:
      - master

jobs:
  prepare-release:
    name: Prepare release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Get current version
        shell: bash
        run: |
          CURRENT_VERSION=$(jq -r '.version' package.json)
          echo "CURRENT_VERSION=$CURRENT_VERSION" >> $GITHUB_ENV
      - uses: actions/checkout@v2
        with:
          repository: pusher/public_actions
          path: .github/actions
      - uses: ./.github/actions/prepare-version-bump
        id: bump
        with:
          current_version: ${{ env.CURRENT_VERSION }}
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - name: Push
        shell: bash
        run: |
          echo "$(jq '.version = "${{ steps.bump.outputs.new_version }}"' package.json)" > package.json

          make build_all

          git status

          git add package.json package-lock.json CHANGELOG.md dist/ types/
          git commit -m "Bump to version ${{ steps.bump.outputs.new_version }}"
          git push
