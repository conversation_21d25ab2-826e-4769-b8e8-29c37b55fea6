module.exports={A:{A:{"1":"A B","2":"K D E F VC"},B:{"1":"0 1 2 3 4 5 6 7 8 9 C L M G N O P Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s y z AB I"},C:{"1":"0 1 2 3 4 5 6 7 8 9 WC 9B J BB K D E F A B C L M G N O P CB t u v w x DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB AC lB BC mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B Q H R CC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s y z AB I 3B DC EC XC YC ZC"},D:{"1":"0 1 2 3 4 5 6 7 8 9 F A B C L M G N O P CB t u v w x DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB AC lB BC mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s y z AB I 3B DC EC","2":"J BB K D E"},E:{"1":"K D E F A B C L M G bC cC dC eC GC 4B 5B fC gC hC HC IC 6B iC 7B JC KC LC MC NC jC 8B OC PC QC RC SC kC","2":"J BB aC FC"},F:{"1":"B C G N O P CB t u v w x DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B Q H R CC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s nC oC 4B TC pC 5B","2":"F lC mC"},G:{"4":"E FC qC UC rC sC tC uC vC wC xC yC zC 0C 1C 2C 3C 4C 5C 6C 7C 8C 9C HC IC 6B AD 7B JC KC LC MC NC BD 8B OC PC QC RC SC"},H:{"4":"CD"},I:{"4":"9B J I DD ED FD GD UC HD ID"},J:{"1":"A","4":"D"},K:{"4":"A B C H 4B TC 5B"},L:{"4":"I"},M:{"4":"3B"},N:{"4":"A B"},O:{"4":"6B"},P:{"4":"J t u v w x JD KD LD MD ND GC OD PD QD RD SD 7B 8B TD UD"},Q:{"1":"VD"},R:{"4":"WD"},S:{"2":"XD YD"}},B:1,C:"Spellcheck attribute",D:true};
