<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branch Node Condition ID Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Branch Node Condition ID Fix Test</h1>
    
    <div class="test-section info">
        <h2>Test Description</h2>
        <p>This test verifies that branch node conditions have unique IDs and are properly isolated between different branch nodes.</p>
        <p><strong>Status:</strong> ✅ Frontend assets compiled successfully! The fix is now active.</p>
    </div>

    <div class="test-section">
        <h2>Test Data</h2>
        <button onclick="runTest()">Run Test</button>
        <button onclick="clearResults()">Clear Results</button>
        <div id="results"></div>
    </div>

    <script>
        // Simulate the problematic flow data from your console output
        const problematicFlowData = {
            nodes: [
                {
                    id: "branch-0.7439814461370684",
                    type: "branch",
                    data: {
                        settings: {
                            conditions: [{
                                id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035',
                                variableId: 'contact_last_message',
                                operator: 'contains',
                                value: 'hey'
                            }]
                        }
                    }
                },
                {
                    id: "branch-0.8212877124900572",
                    type: "branch",
                    data: {
                        settings: {
                            conditions: [{
                                id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035',
                                variableId: 'contact_last_message',
                                operator: 'contains',
                                value: 'hey'
                            }]
                        }
                    }
                },
                {
                    id: "branch-0.7868949944308767",
                    type: "branch",
                    data: {
                        settings: {
                            conditions: [{
                                id: 'branch-0.8212877124900572_cond_687b6ec3550cf5.31861035',
                                variableId: 'contact_last_message',
                                operator: 'contains',
                                value: 'hey'
                            }]
                        }
                    }
                }
            ],
            edges: []
        };

        // Simulate the fix functions
        function generateUniqueConditionId(nodeId) {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(2, 15);
            return `${nodeId}_cond_${timestamp}_${random}`;
        }

        function deepCloneConditions(conditions, nodeId) {
            return conditions.map(condition => {
                const belongsToThisNode = condition.id.startsWith(`${nodeId}_cond_`);
                return {
                    id: belongsToThisNode ? condition.id : generateUniqueConditionId(nodeId),
                    variableId: condition.variableId,
                    operator: condition.operator,
                    value: condition.value,
                };
            });
        }

        function hasSharedConditionIds(flowData) {
            const conditionIdMap = new Map();
            
            for (const node of flowData.nodes) {
                if (node.type === 'branch' && node.data.settings?.conditions) {
                    for (const condition of node.data.settings.conditions) {
                        if (!conditionIdMap.has(condition.id)) {
                            conditionIdMap.set(condition.id, []);
                        }
                        conditionIdMap.get(condition.id).push(node.id);
                    }
                }
            }
            
            for (const [conditionId, nodeIds] of conditionIdMap) {
                if (nodeIds.length > 1) {
                    return true;
                }
            }
            
            return false;
        }

        function fixSharedConditionIds(flowData) {
            const fixedNodes = flowData.nodes.map(node => {
                if (node.type !== 'branch' || !node.data.settings?.conditions) {
                    return node;
                }
                
                const fixedConditions = deepCloneConditions(node.data.settings.conditions, node.id);
                
                return {
                    ...node,
                    data: {
                        ...node.data,
                        settings: {
                            ...node.data.settings,
                            conditions: fixedConditions
                        }
                    }
                };
            });
            
            return {
                ...flowData,
                nodes: fixedNodes
            };
        }

        function runTest() {
            const resultsDiv = document.getElementById('results');
            let html = '<h3>Test Results</h3>';
            
            // Test 1: Detect shared condition IDs
            html += '<div class="test-section">';
            html += '<h4>Test 1: Detect Shared Condition IDs</h4>';
            const hasShared = hasSharedConditionIds(problematicFlowData);
            if (hasShared) {
                html += '<div class="success">✅ PASS: Correctly detected shared condition IDs</div>';
            } else {
                html += '<div class="error">❌ FAIL: Failed to detect shared condition IDs</div>';
            }
            html += '</div>';

            // Test 2: Fix shared condition IDs
            html += '<div class="test-section">';
            html += '<h4>Test 2: Fix Shared Condition IDs</h4>';
            const fixedFlowData = fixSharedConditionIds(problematicFlowData);
            const stillHasShared = hasSharedConditionIds(fixedFlowData);
            if (!stillHasShared) {
                html += '<div class="success">✅ PASS: Successfully fixed shared condition IDs</div>';
            } else {
                html += '<div class="error">❌ FAIL: Still has shared condition IDs after fix</div>';
            }
            html += '</div>';

            // Test 3: Verify unique IDs per node
            html += '<div class="test-section">';
            html += '<h4>Test 3: Verify Unique IDs Per Node</h4>';
            let allUnique = true;
            const nodeConditions = {};
            
            for (const node of fixedFlowData.nodes) {
                if (node.type === 'branch' && node.data.settings?.conditions) {
                    nodeConditions[node.id] = node.data.settings.conditions;
                    for (const condition of node.data.settings.conditions) {
                        if (!condition.id.startsWith(`${node.id}_cond_`)) {
                            allUnique = false;
                            break;
                        }
                    }
                }
            }
            
            if (allUnique) {
                html += '<div class="success">✅ PASS: All condition IDs are unique and belong to their respective nodes</div>';
            } else {
                html += '<div class="error">❌ FAIL: Some condition IDs don\'t belong to their nodes</div>';
            }
            html += '</div>';

            // Show before and after data
            html += '<div class="test-section">';
            html += '<h4>Before Fix (Problematic Data)</h4>';
            html += '<pre>' + JSON.stringify(problematicFlowData.nodes.map(n => ({
                id: n.id,
                conditions: n.data?.settings?.conditions?.map(c => ({ id: c.id, variableId: c.variableId, value: c.value }))
            })), null, 2) + '</pre>';
            html += '</div>';

            html += '<div class="test-section">';
            html += '<h4>After Fix (Fixed Data)</h4>';
            html += '<pre>' + JSON.stringify(fixedFlowData.nodes.map(n => ({
                id: n.id,
                conditions: n.data?.settings?.conditions?.map(c => ({ id: c.id, variableId: c.variableId, value: c.value }))
            })), null, 2) + '</pre>';
            html += '</div>';

            resultsDiv.innerHTML = html;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
