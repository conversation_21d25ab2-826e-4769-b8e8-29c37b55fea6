/**
 * API URL utility for handling different environments (localhost vs production)
 * This ensures all API calls work correctly regardless of the deployment environment
 */

declare global {
  interface Window {
    baseUrl: string;
  }
}

/**
 * Get the base URL for API calls
 * This handles both localhost and production environments
 */
export const getBaseUrl = (): string => {
  // Use the baseUrl set by the server if available
  if (typeof window !== 'undefined' && window.baseUrl) {
    return window.baseUrl;
  }
  
  // Fallback to current origin
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  // Server-side fallback
  return '';
};

/**
 * Build a complete API URL from a relative path
 * @param path - The relative API path (e.g., '/flowmaker/update/123')
 * @returns Complete URL with proper base
 */
export const buildApiUrl = (path: string): string => {
  const baseUrl = getBaseUrl();
  
  // Remove leading slash if present to avoid double slashes
  const cleanPath = path.startsWith('/') ? path.substring(1) : path;
  
  // Ensure base URL doesn't end with slash
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
  
  return `${cleanBaseUrl}/${cleanPath}`;
};

/**
 * Enhanced fetch function that automatically handles URL resolution
 * @param url - The URL or path to fetch
 * @param options - Fetch options
 * @returns Promise<Response>
 */
export const apiFetch = async (url: string, options?: RequestInit): Promise<Response> => {
  let finalUrl = url;
  
  // Only modify relative URLs that start with specific patterns
  if (typeof url === 'string' && 
      (url.startsWith('/ai/') || 
       url.startsWith('/flowmaker/') || 
       url === '/flowmakermedia') && 
      !url.startsWith('http')) {
    
    finalUrl = buildApiUrl(url);
    console.log('API URL resolved:', { original: url, resolved: finalUrl });
  }
  
  return fetch(finalUrl, options);
};

/**
 * Get flow ID from current URL
 * Handles different URL patterns in different environments
 */
export const getFlowIdFromUrl = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  const pathSegments = window.location.pathname.split('/').filter(Boolean);
  
  // Look for flow ID in different positions depending on URL structure
  // e.g., /flowmaker/edit/123 or /zaptra/flowmaker/edit/123
  const flowmakerIndex = pathSegments.findIndex(segment => segment === 'flowmaker');
  
  if (flowmakerIndex !== -1 && flowmakerIndex + 2 < pathSegments.length) {
    return pathSegments[flowmakerIndex + 2]; // flowmaker/edit/{id}
  }
  
  // Fallback: assume last segment is the ID
  return pathSegments[pathSegments.length - 1] || null;
};

/**
 * Build flowmaker-specific URLs
 */
export const flowmakerUrls = {
  updateFlow: (flowId: string | number) => `/flowmaker/update/${flowId}`,
  trainingData: (flowId: string | number) => `/ai/training-data/${flowId}`,
  processWebsite: () => '/ai/process-website',
  processFaq: () => '/ai/process-faq',
  processFile: () => '/ai/process-file',
  deleteDocument: (documentId: string | number) => `/ai/document/${documentId}`,
  uploadMedia: () => '/flowmakermedia',
};

/**
 * Environment detection utilities
 */
export const environment = {
  isLocalhost: (): boolean => {
    if (typeof window === 'undefined') return false;
    return window.location.hostname === 'localhost' || 
           window.location.hostname === '127.0.0.1' ||
           window.location.hostname.startsWith('192.168.');
  },
  
  isProduction: (): boolean => {
    return !environment.isLocalhost();
  },
  
  getEnvironmentName: (): string => {
    return environment.isLocalhost() ? 'localhost' : 'production';
  }
};

/**
 * Debug logging for URL resolution
 */
export const debugUrl = (context: string, originalUrl: string, resolvedUrl?: string) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[${environment.getEnvironmentName()}] ${context}:`, {
      original: originalUrl,
      resolved: resolvedUrl || originalUrl,
      baseUrl: getBaseUrl()
    });
  }
};
