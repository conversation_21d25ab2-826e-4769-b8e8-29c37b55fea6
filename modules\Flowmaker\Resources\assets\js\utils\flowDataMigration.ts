/**
 * Flow Data Migration Utilities
 * 
 * This module contains utilities to fix existing flow data that may have
 * shared condition IDs between branch nodes.
 */

import { BranchCondition } from '@/types/flow';

interface FlowNode {
  id: string;
  type: string;
  data: {
    settings?: {
      conditions?: BranchCondition[];
    };
    [key: string]: any;
  };
  [key: string]: any;
}

interface FlowData {
  nodes: FlowNode[];
  edges: any[];
}

/**
 * Generate a unique condition ID for a specific branch node
 */
const generateUniqueConditionId = (nodeId: string): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  return `${nodeId}_cond_${timestamp}_${random}`;
};

/**
 * Check if a condition ID is shared between multiple nodes
 */
const isSharedConditionId = (conditionId: string, nodeId: string, allNodes: FlowNode[]): boolean => {
  let foundCount = 0;
  
  for (const node of allNodes) {
    if (node.type === 'branch' && node.data.settings?.conditions) {
      for (const condition of node.data.settings.conditions) {
        if (condition.id === conditionId) {
          foundCount++;
          if (foundCount > 1) {
            return true; // Found in multiple nodes
          }
        }
      }
    }
  }
  
  return false;
};

/**
 * Fix shared condition IDs in flow data
 */
export const fixSharedConditionIds = (flowData: FlowData): FlowData => {
  console.log('🔧 Starting flow data migration to fix shared condition IDs...');
  
  const fixedNodes = flowData.nodes.map(node => {
    if (node.type !== 'branch' || !node.data.settings?.conditions) {
      return node; // Not a branch node, return as-is
    }
    
    const fixedConditions = node.data.settings.conditions.map(condition => {
      // Check if this condition ID is shared or doesn't belong to this node
      const isShared = isSharedConditionId(condition.id, node.id, flowData.nodes);
      const belongsToThisNode = condition.id.startsWith(`${node.id}_cond_`);

      if (isShared || !belongsToThisNode) {
        const newId = generateUniqueConditionId(node.id);
        console.log(`🔄 Migrating condition ID: ${condition.id} → ${newId} (Node: ${node.id})`);

        return {
          ...condition,
          id: newId
        };
      }

      return condition; // Keep existing ID if it's unique and belongs to this node
    });
    
    return {
      ...node,
      data: {
        ...node.data,
        settings: {
          ...node.data.settings,
          conditions: fixedConditions
        }
      }
    };
  });
  
  console.log('✅ Flow data migration completed');
  
  return {
    ...flowData,
    nodes: fixedNodes
  };
};

/**
 * Detect if flow data has shared condition IDs
 */
export const hasSharedConditionIds = (flowData: FlowData): boolean => {
  const conditionIdMap = new Map<string, string[]>(); // conditionId -> nodeIds[]
  
  // Collect all condition IDs and their associated node IDs
  for (const node of flowData.nodes) {
    if (node.type === 'branch' && node.data.settings?.conditions) {
      for (const condition of node.data.settings.conditions) {
        if (!conditionIdMap.has(condition.id)) {
          conditionIdMap.set(condition.id, []);
        }
        conditionIdMap.get(condition.id)!.push(node.id);
      }
    }
  }
  
  // Check if any condition ID is used by multiple nodes
  for (const [conditionId, nodeIds] of conditionIdMap.entries()) {
    if (nodeIds.length > 1) {
      console.warn(`⚠️ Shared condition ID detected: ${conditionId} used by nodes: ${nodeIds.join(', ')}`);
      return true;
    }
  }
  
  return false;
};

/**
 * Get statistics about condition IDs in flow data
 */
export const getConditionIdStats = (flowData: FlowData): {
  totalBranchNodes: number;
  totalConditions: number;
  sharedConditionIds: string[];
  uniqueConditionIds: number;
} => {
  const conditionIdMap = new Map<string, string[]>();
  let totalBranchNodes = 0;
  let totalConditions = 0;
  
  for (const node of flowData.nodes) {
    if (node.type === 'branch') {
      totalBranchNodes++;
      
      if (node.data.settings?.conditions) {
        totalConditions += node.data.settings.conditions.length;
        
        for (const condition of node.data.settings.conditions) {
          if (!conditionIdMap.has(condition.id)) {
            conditionIdMap.set(condition.id, []);
          }
          conditionIdMap.get(condition.id)!.push(node.id);
        }
      }
    }
  }
  
  const sharedConditionIds = Array.from(conditionIdMap.entries())
    .filter(([_, nodeIds]) => nodeIds.length > 1)
    .map(([conditionId, _]) => conditionId);
  
  return {
    totalBranchNodes,
    totalConditions,
    sharedConditionIds,
    uniqueConditionIds: conditionIdMap.size
  };
};

/**
 * Auto-migrate flow data if needed
 */
export const autoMigrateFlowData = (flowData: FlowData): FlowData => {
  if (hasSharedConditionIds(flowData)) {
    console.log('🚨 Shared condition IDs detected, auto-migrating...');
    return fixSharedConditionIds(flowData);
  }
  
  console.log('✅ No shared condition IDs detected, no migration needed');
  return flowData;
};

/**
 * Validate flow data integrity
 */
export const validateFlowData = (flowData: FlowData): {
  isValid: boolean;
  issues: string[];
  stats: ReturnType<typeof getConditionIdStats>;
} => {
  const issues: string[] = [];
  const stats = getConditionIdStats(flowData);
  
  // Check for shared condition IDs
  if (stats.sharedConditionIds.length > 0) {
    issues.push(`Found ${stats.sharedConditionIds.length} shared condition IDs: ${stats.sharedConditionIds.join(', ')}`);
  }
  
  // Check for malformed condition IDs
  for (const node of flowData.nodes) {
    if (node.type === 'branch' && node.data.settings?.conditions) {
      for (const condition of node.data.settings.conditions) {
        if (!condition.id || condition.id.trim() === '') {
          issues.push(`Empty condition ID found in node ${node.id}`);
        }
        
        if (!condition.id.includes('_cond_')) {
          issues.push(`Malformed condition ID format in node ${node.id}: ${condition.id}`);
        }
      }
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    stats
  };
};
