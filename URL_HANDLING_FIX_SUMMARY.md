# URL Handling Fix for Production and Localhost

## Problem Analysis

The application was experiencing 404 errors when saving flows, specifically:
```
Failed to load resource: the server responded with a status of 404 (Not Found)
Error saving flow: Error: Failed to save flow
```

### Root Cause
The issue was caused by **hardcoded relative URLs** that didn't account for different deployment environments:

1. **Localhost**: URLs like `/flowmaker/update/123` work directly
2. **Production**: URLs need to include the base path (e.g., `https://demo.zaptra.in/flowmaker/update/123`)

The existing fetch override in `index.blade.php` only handled `/ai/` and `/flowmakermedia` URLs, but **missed the crucial `/flowmaker/` URLs** used for saving flows.

## Solution Implemented

### 1. Enhanced Fetch Override
Updated the global fetch override to handle all flowmaker-related URLs:

```javascript
// Before: Only handled /ai/ and /flowmakermedia
if (url.startsWith('/ai/') || url === '/flowmakermedia')

// After: Handles all flowmaker URLs
if (url.startsWith('/ai/') || 
    url.startsWith('/flowmaker/') || 
    url === '/flowmakermedia')
```

### 2. Comprehensive API URL Utility
Created `modules/Flowmaker/Resources/assets/js/utils/apiUrl.ts` with:

#### Core Functions
- `getBaseUrl()`: Intelligently determines base URL for current environment
- `buildApiUrl(path)`: Constructs complete URLs from relative paths
- `apiFetch(url, options)`: Enhanced fetch with automatic URL resolution
- `getFlowIdFromUrl()`: Extracts flow ID from various URL patterns

#### Environment Detection
- `environment.isLocalhost()`: Detects localhost/development environments
- `environment.isProduction()`: Detects production environments
- `environment.getEnvironmentName()`: Returns current environment name

#### Predefined URL Builders
```typescript
const flowmakerUrls = {
  updateFlow: (flowId) => `/flowmaker/update/${flowId}`,
  trainingData: (flowId) => `/ai/training-data/${flowId}`,
  processWebsite: () => '/ai/process-website',
  processFaq: () => '/ai/process-faq',
  processFile: () => '/ai/process-file',
  deleteDocument: (id) => `/ai/document/${id}`,
  uploadMedia: () => '/flowmakermedia',
};
```

### 3. Updated Components
Modified key components to use the new URL utility:

#### FlowCanvas.tsx
```typescript
// Before
const response = await fetch(`/flowmaker/update/${window.data.flow.id}`, {

// After  
const updateUrl = flowmakerUrls.updateFlow(window.data.flow.id);
const response = await apiFetch(updateUrl, {
```

#### DataSidebar.tsx
```typescript
// Before
const flowId = window.location.pathname.split('/').pop();
const response = await fetch(`/ai/training-data/${flowId}`, {

// After
const flowId = getFlowIdFromUrl();
const trainingDataUrl = flowmakerUrls.trainingData(flowId);
const response = await apiFetch(trainingDataUrl, {
```

## Environment Compatibility

### Localhost Development
- Base URL: `http://localhost:8000`
- Flow save URL: `http://localhost:8000/flowmaker/update/123`
- Works with standard Laravel development server

### Production (demo.zaptra.in)
- Base URL: `https://demo.zaptra.in`
- Flow save URL: `https://demo.zaptra.in/flowmaker/update/123`
- Handles subdirectory deployments automatically

### Subdirectory Deployments
- Base URL: `https://example.com/zaptra`
- Flow save URL: `https://example.com/zaptra/flowmaker/update/123`
- Automatically detected and handled

## Testing Strategy

### Automated Tests
Created comprehensive test suite in `apiUrl.test.ts`:
- URL building for different environments
- Flow ID extraction from various URL patterns
- Environment detection accuracy
- End-to-end integration scenarios

### Manual Testing Checklist
- [ ] Flow saving works on localhost
- [ ] Flow saving works on demo.zaptra.in
- [ ] AI training data loading works
- [ ] Website processing works
- [ ] File upload works
- [ ] Document deletion works
- [ ] All API calls use correct base URLs

## Debugging Features

### Console Logging
The solution includes comprehensive logging:
```javascript
console.log('Modified URL:', url);
console.log(`${method} request intercepted:`, { url, options });
debugUrl('Flow Save', updateUrl);
```

### Environment Indicators
```javascript
console.log(`[${environment.getEnvironmentName()}] API Call:`, {
  original: '/flowmaker/update/123',
  resolved: 'https://demo.zaptra.in/flowmaker/update/123',
  baseUrl: 'https://demo.zaptra.in'
});
```

## Files Modified

1. **modules/Flowmaker/Resources/views/index.blade.php**
   - Enhanced fetch override to handle `/flowmaker/` URLs
   - Improved logging for debugging

2. **modules/Flowmaker/Resources/assets/js/utils/apiUrl.ts** (NEW)
   - Comprehensive URL handling utility
   - Environment detection
   - Predefined URL builders

3. **modules/Flowmaker/Resources/assets/js/components/flow/FlowCanvas.tsx**
   - Updated flow saving to use new API utility
   - Added debugging for save operations

4. **modules/Flowmaker/Resources/assets/js/components/flow/DataSidebar.tsx**
   - Updated AI API calls to use new utility
   - Improved flow ID extraction

5. **modules/Flowmaker/Resources/assets/js/utils/apiUrl.test.ts** (NEW)
   - Comprehensive test suite for URL handling

## Deployment Notes

### Safe Deployment
- ✅ Backward compatible with existing functionality
- ✅ No breaking changes to API endpoints
- ✅ Graceful fallbacks for edge cases
- ✅ No database changes required

### Cache Clearing
- Removed compiled view cache: `storage/framework/views/60588da61d3dc94b513d748c54ce2648.php`
- Laravel will regenerate with updated fetch override

### Verification Steps
1. Test flow saving on both localhost and production
2. Verify console shows "Modified URL" logs
3. Check that 404 errors are resolved
4. Confirm all AI features work correctly

## Future Improvements

1. **TypeScript Integration**: Add proper TypeScript types for window.baseUrl
2. **Error Handling**: Enhanced error messages for URL resolution failures
3. **Performance**: Consider caching base URL resolution
4. **Monitoring**: Add metrics for API call success rates by environment

## Troubleshooting

### Common Issues
1. **Still getting 404s**: Check browser cache, hard refresh (Ctrl+F5)
2. **Console errors**: Verify window.baseUrl is set correctly
3. **Wrong URLs**: Check environment detection in console logs

### Debug Commands
```javascript
// Check current environment
console.log(environment.getEnvironmentName());

// Check base URL
console.log(getBaseUrl());

// Test URL building
console.log(buildApiUrl('/flowmaker/update/123'));
```
