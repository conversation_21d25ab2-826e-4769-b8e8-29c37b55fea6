import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ReactFlowProvider } from '@xyflow/react';
import BranchNode from './BranchNode';
import { BranchCondition } from '@/types/flow';

// Mock the hooks
jest.mock('@/hooks/useFlowVariables', () => ({
  useFlowVariables: () => ({
    groupedVariables: {
      'Contact': [
        { value: 'contact.name', label: 'Contact Name' },
        { value: 'contact.email', label: 'Contact Email' }
      ]
    }
  })
}));

// Mock React Flow
const mockSetNodes = jest.fn();
jest.mock('@xyflow/react', () => ({
  ...jest.requireActual('@xyflow/react'),
  useReactFlow: () => ({
    setNodes: mockSetNodes,
  }),
  Handle: ({ children, ...props }: any) => <div data-testid="handle" {...props}>{children}</div>,
  Position: {
    Left: 'left',
    Right: 'right'
  }
}));

describe('BranchNode', () => {
  const mockConditions: BranchCondition[] = [
    {
      id: 'condition1',
      variableId: 'contact.name',
      operator: 'equals',
      value: 'John'
    }
  ];

  const mockData = {
    settings: {
      conditions: mockConditions
    }
  };

  beforeEach(() => {
    mockSetNodes.mockClear();
  });

  it('should render without crashing', () => {
    render(
      <ReactFlowProvider>
        <BranchNode id="branch1" data={mockData} />
      </ReactFlowProvider>
    );
    
    expect(screen.getByText('Branch')).toBeInTheDocument();
  });

  it('should initialize with deep cloned conditions', () => {
    const { rerender } = render(
      <ReactFlowProvider>
        <BranchNode id="branch1" data={mockData} />
      </ReactFlowProvider>
    );

    // Verify initial condition is displayed
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();

    // Change the original data reference
    const modifiedData = {
      settings: {
        conditions: [
          {
            id: 'condition1',
            variableId: 'contact.name',
            operator: 'equals' as const,
            value: 'Modified'
          }
        ]
      }
    };

    // Re-render with modified data
    rerender(
      <ReactFlowProvider>
        <BranchNode id="branch1" data={modifiedData} />
      </ReactFlowProvider>
    );

    // Should update to show new value
    expect(screen.getByDisplayValue('Modified')).toBeInTheDocument();
  });

  it('should not affect other branch nodes when updating conditions', () => {
    // Render two branch nodes with the same initial data
    const { container } = render(
      <ReactFlowProvider>
        <div>
          <BranchNode id="branch1" data={mockData} />
          <BranchNode id="branch2" data={mockData} />
        </div>
      </ReactFlowProvider>
    );

    // Get all value inputs
    const valueInputs = screen.getAllByDisplayValue('John');
    expect(valueInputs).toHaveLength(2);

    // Modify the first branch's condition value
    fireEvent.change(valueInputs[0], { target: { value: 'Jane' } });

    // Verify that setNodes was called with the correct node ID
    expect(mockSetNodes).toHaveBeenCalledWith(expect.any(Function));
    
    // Get the function that was passed to setNodes
    const setNodesFunction = mockSetNodes.mock.calls[0][0];
    
    // Mock nodes array
    const mockNodes = [
      { id: 'branch1', data: mockData },
      { id: 'branch2', data: mockData }
    ];
    
    // Call the function with mock nodes
    const updatedNodes = setNodesFunction(mockNodes);
    
    // Verify only the first node was updated
    expect(updatedNodes[0].data.settings.conditions[0].value).toBe('Jane');
    expect(updatedNodes[1].data.settings.conditions[0].value).toBe('John');
  });

  it('should create deep clones when adding conditions', () => {
    render(
      <ReactFlowProvider>
        <BranchNode id="branch1" data={{ settings: { conditions: [] } }} />
      </ReactFlowProvider>
    );

    // Click add condition button
    const addButton = screen.getByText('Add Condition');
    fireEvent.click(addButton);

    // Verify setNodes was called
    expect(mockSetNodes).toHaveBeenCalled();
    
    const setNodesFunction = mockSetNodes.mock.calls[0][0];
    const mockNodes = [{ id: 'branch1', data: { settings: { conditions: [] } } }];
    const updatedNodes = setNodesFunction(mockNodes);
    
    // Verify a new condition was added
    expect(updatedNodes[0].data.settings.conditions).toHaveLength(1);
    expect(updatedNodes[0].data.settings.conditions[0]).toHaveProperty('id');
    expect(updatedNodes[0].data.settings.conditions[0].variableId).toBe('');
    expect(updatedNodes[0].data.settings.conditions[0].operator).toBe('equals');
    expect(updatedNodes[0].data.settings.conditions[0].value).toBe('');
  });
});
